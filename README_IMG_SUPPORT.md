# FleetOS Prepare Script - IMG File Support

## Overview

The `fleetos_prepare.sh` script has been enhanced to support both single partition images (`.ext4`) and whole disk images (`.img`).

## Supported Image Types

### 1. Single Partition Images (.ext4)
- **Description**: Direct ext4 filesystem images
- **Usage**: Same as before - the script mounts the image directly
- **Example**: `rootfs.ext4`

### 2. Whole Disk Images (.img)
- **Description**: Complete disk images containing partition table and multiple partitions
- **Usage**: The script automatically detects and extracts the rootfs partition
- **Example**: `complete_system.img`

## How It Works

### For .ext4 files:
1. Validates file extension
2. Mounts the image directly as a loop device
3. Extracts kernel and updates fstab
4. Creates bootable image

### For .img files:
1. Validates file extension
2. Detects if it's a whole disk image (contains partition table)
3. If whole disk image:
   - Uses `fdisk` to get partition information
   - Extracts the specified partition (default: 2nd) directly using `dd` to a temporary .ext4 file
4. Mounts the extracted rootfs partition
5. Extracts kernel and updates fstab
6. Creates bootable image using the extracted rootfs
7. Cleans up temporary files and loop devices

## Usage

```bash
# For .ext4 files (existing functionality)
./fleetos_prepare.sh rootfs.ext4 bootloader.bin

# For .img files (new functionality)
./fleetos_prepare.sh complete_system.img bootloader.bin
```

## Configuration

To extract a different partition from .img files, modify the `ROOTFS_PARTITION_NUMBER` constant at the top of the script:

```bash
# Configuration: Which partition to extract from .img files
ROOTFS_PARTITION_NUMBER=2  # Change this to extract a different partition
```

Examples:
- `ROOTFS_PARTITION_NUMBER=1` - Extract 1st partition
- `ROOTFS_PARTITION_NUMBER=2` - Extract 2nd partition (default)
- `ROOTFS_PARTITION_NUMBER=3` - Extract 3rd partition

## Key Features

- **Automatic Detection**: Automatically detects whether an .img file is a whole disk image or single partition
- **Configurable Partition Selection**: Uses configurable `ROOTFS_PARTITION_NUMBER` constant (default: 2)
- **Cleanup**: Automatically cleans up temporary files and loop devices on exit
- **Error Handling**: Proper error handling with cleanup on script interruption
- **Backward Compatibility**: Existing .ext4 workflows remain unchanged

## Requirements

- `fdisk` and `dd` utilities (standard on most Linux systems)
- The existing `target_disk_setup` tool

## Error Handling

The script includes comprehensive error handling:
- Validates input files exist
- Checks for partition existence
- Properly cleans up resources on exit or interruption
- Provides informative error messages

## Temporary Files

When processing .img files, the script creates:
- `${QEMU_IMAGE_DIR}/extracted_rootfs.ext4` - Extracted rootfs partition

All temporary files are automatically cleaned up on script completion or interruption.
