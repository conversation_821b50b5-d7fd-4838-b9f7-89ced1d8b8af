# FleetOS Prepare Script - IMG File Support

## Overview

The `fleetos_prepare.sh` script has been enhanced to support both single partition images (`.ext4`) and whole disk images (`.img`).

## Supported Image Types

### 1. Single Partition Images (.ext4)
- **Description**: Direct ext4 filesystem images
- **Usage**: Same as before - the script mounts the image directly
- **Example**: `rootfs.ext4`

### 2. Whole Disk Images (.img)
- **Description**: Complete disk images containing partition table and multiple partitions
- **Usage**: The script automatically detects and extracts the rootfs partition
- **Example**: `complete_system.img`

## How It Works

### For .ext4 files:
1. Validates file extension
2. Mounts the image directly as a loop device
3. Extracts kernel and updates fstab
4. Creates bootable image

### For .img files:
1. Validates file extension
2. Detects if it's a whole disk image (contains partition table)
3. If whole disk image:
   - Creates loop device for the entire image
   - Probes for partitions
   - Finds the largest ext4 partition (assumed to be rootfs)
   - Extracts the rootfs partition to a temporary .ext4 file
4. Mounts the extracted rootfs partition
5. Extracts kernel and updates fstab
6. Creates bootable image using the extracted rootfs
7. Cleans up temporary files and loop devices

## Usage

```bash
# For .ext4 files (existing functionality)
./fleetos_prepare.sh rootfs.ext4 bootloader.bin

# For .img files (new functionality)
./fleetos_prepare.sh complete_system.img bootloader.bin
```

## Key Features

- **Automatic Detection**: Automatically detects whether an .img file is a whole disk image or single partition
- **Robust Partition Detection**: Finds the largest ext4 partition as the rootfs
- **Cleanup**: Automatically cleans up temporary files and loop devices on exit
- **Error Handling**: Proper error handling with cleanup on script interruption
- **Backward Compatibility**: Existing .ext4 workflows remain unchanged

## Requirements

- `sudo` access for loop device operations
- `fdisk`, `partprobe`, `file`, `blockdev`, and `dd` utilities
- The existing `target_disk_setup` tool

## Error Handling

The script includes comprehensive error handling:
- Validates input files exist
- Checks for required tools
- Properly cleans up resources on exit or interruption
- Provides informative error messages

## Temporary Files

When processing .img files, the script creates:
- `${QEMU_IMAGE_DIR}/extracted_rootfs.ext4` - Extracted rootfs partition
- Loop devices for accessing partitions

All temporary files and devices are automatically cleaned up on script completion or interruption.
