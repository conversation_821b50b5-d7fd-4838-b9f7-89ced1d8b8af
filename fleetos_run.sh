#!/bin/bash
#
# Run QEMU with the specified bootable image
#
# Usage: $0 <bootable_image_path> [partition_number]
#

set -e

SCRIPT_DIR="$(dirname "$(realpath "$0")")"
QEMU_IMAGE_DIR="$SCRIPT_DIR/qemu_images"
KERNEL_IMAGE_PATH="$QEMU_IMAGE_DIR/kernel.img"
QEMU_INSTALL_DIR="$SCRIPT_DIR/qemu/qemu_install"

# Get bootable image path from argument
BOOTABLE_IMAGE_PATH="$1"
PART_NUMBER="$2"

# Set default partition number if not provided
if [ -z "$PART_NUMBER" ]; then
    PART_NUMBER=2
fi

# Validate arguments
if [ -z "$BOOTABLE_IMAGE_PATH" ]; then
    echo "Usage: $0 <bootable_image_path> [partition_number]"
    echo "  bootable_image_path: Path to the bootable image file"
    echo "  partition_number: Partition to boot from (default: 2)"
    exit 1
fi

if [ ! -f "$BOOTABLE_IMAGE_PATH" ]; then
    echo "Bootable image not found at: $BOOTABLE_IMAGE_PATH"
    exit 1
fi

if [ ! -f "$KERNEL_IMAGE_PATH" ]; then
    echo "Kernel image not found at: $KERNEL_IMAGE_PATH"
    echo "Run fleetos_prepare.sh first to extract the kernel image"
    exit 1
fi

#
# cortex-a72 is used for Raspberry Pi 4
# cortex-a76 is used for Raspberry Pi 5
#
CORTEX="cortex-a76"

echo "Starting QEMU with:"
echo "  Bootable image: $BOOTABLE_IMAGE_PATH"
echo "  Kernel image:   $KERNEL_IMAGE_PATH"
echo "  Boot partition: $PART_NUMBER"
echo "  CPU:            $CORTEX"
echo

$QEMU_INSTALL_DIR/bin/qemu-system-aarch64 \
    -M virt \
    -cpu $CORTEX \
    -smp 4 \
    -m 8G \
    -kernel "${KERNEL_IMAGE_PATH}" \
    -append "root=/dev/vda${PART_NUMBER} console=ttyAMA0" \
    -drive if=none,file="${BOOTABLE_IMAGE_PATH}",format=raw,id=usbdisk \
    -device virtio-blk-device,drive=usbdisk \
    -netdev user,id=net0,hostfwd=tcp::2222-:22 \
    -device virtio-net-device,netdev=net0 \
    -serial mon:stdio \
    -nographic