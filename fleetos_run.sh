#!/bin/bash

set -e

SCRIPT_DIR="$(dirname "$(realpath "$0")")"
QEMU_IMAGE_DIR="$SCRIPT_DIR/qemu_images"
KERNEL_IMAGE_PATH="$QEMU_IMAGE_DIR/kernel.img"
BOOTABLE_IMAGE_DIR="../package-to-image-placer/image.img"
QEMU_INSTALL_DIR="$SCRIPT_DIR/qemu/qemu_install"

#
# cortext-a72 is used for Raspberry Pi 4
# cortex-a76 is used for Raspberry Pi 5
#
CORTEX="cortex-a76"

#
# Select a part number to use to boot to...
#
PART_NUMBER=2

$QEMU_INSTALL_DIR/bin/qemu-system-aarch64 \
    -M virt \
    -cpu $CORTEX \
    -smp 4 \
    -m 8G \
    -kernel "${KERNEL_IMAGE_PATH}" \
    -append "root=/dev/vda${PART_NUMBER} console=ttyAMA0" \
    -drive if=none,file=${BOOTABLE_IMAGE_DIR},format=raw,id=usbdisk \
    -device virtio-blk-device,drive=usbdisk \
    -netdev user,id=net0,hostfwd=tcp::2222-:22 \
    -device virtio-net-device,netdev=net0 \
    -serial mon:stdio \
    -nographic