#!/bin/bash
#
# Run QEMU with direct partition access using offset
# For .img files: calculates partition offset for direct access
# For .ext4 files: uses the file directly as a single partition
#

set -e

SCRIPT_DIR="$(dirname "$(realpath "$0")")"
QEMU_IMAGE_DIR="$SCRIPT_DIR/qemu_images"
KERNEL_IMAGE_PATH="$QEMU_IMAGE_DIR/kernel.img"
QEMU_INSTALL_DIR="$SCRIPT_DIR/qemu/qemu_install"

# Selected partition to boot from
PART_NUMBER=2

# Image path - can be either the prepared bootable image or original disk image
IMAGE_PATH="$1"

# Set default image if not provided
if [ -z "$IMAGE_PATH" ]; then
    IMAGE_PATH="$QEMU_IMAGE_DIR/bootable_image.img"
fi

# Validate inputs
if [ ! -f "$IMAGE_PATH" ]; then
    echo "Image not found at: $IMAGE_PATH"
    exit 1
fi

if [ ! -f "$KERNEL_IMAGE_PATH" ]; then
    echo "Kernel image not found at: $KERNEL_IMAGE_PATH"
    echo "Run fleetos_prepare.sh first to extract the kernel image"
    exit 1
fi

#
# cortex-a72 is used for Raspberry Pi 4
# cortex-a76 is used for Raspberry Pi 5
#
CORTEX="cortex-a76"

#
# Calculate partition offset for direct access
#
calculate_partition_offset() {
    local img_path="$1"
    local partition_num="$2"

    # Get partition information using fdisk
    local partition_info=$(fdisk -l "$img_path" 2>/dev/null | grep "^${img_path}${partition_num} ")

    if [ -z "$partition_info" ]; then
        echo "Could not find partition $partition_num in $img_path"
        echo "Available partitions:"
        fdisk -l "$img_path" 2>/dev/null | grep "^${img_path}" || echo "No partitions found"
        return 1
    fi

    # Extract start sector from fdisk output
    # fdisk output format: device boot start end sectors size id type
    local start_sector=$(echo "$partition_info" | awk '{print $2}')

    # Calculate offset in bytes (512 bytes per sector)
    local offset_bytes=$((start_sector * 512))

    echo "$offset_bytes"
}

echo "Starting QEMU with:"
echo "  Image:          $IMAGE_PATH"
echo "  Extension:      .$EXTENSION"
echo "  Kernel:         $KERNEL_IMAGE_PATH"
echo "  Boot partition: $PART_NUMBER"
echo "  CPU:            $CORTEX"

# Check if we need to calculate offset based on file extension
DRIVE_PARAMS="if=none,file=${IMAGE_PATH},format=raw,id=usbdisk"
EXTENSION="${IMAGE_PATH##*.}"

if [ "$EXTENSION" == "img" ]; then
    echo "  Detected .img file, calculating partition offset..."

    OFFSET=$(calculate_partition_offset "$IMAGE_PATH" "$PART_NUMBER")
    if [ $? -eq 0 ] && [ -n "$OFFSET" ]; then
        echo "  Partition offset: $OFFSET bytes"
        DRIVE_PARAMS="if=none,file=${IMAGE_PATH},format=raw,id=usbdisk,offset=${OFFSET}"
        # For offset access, we boot from the first partition of the exposed partition
        PART_NUMBER=1
    else
        echo "  Failed to calculate offset, using whole disk"
    fi
else
    echo "  Using single partition image (.ext4 or prepared bootable image)"
fi

echo

$QEMU_INSTALL_DIR/bin/qemu-system-aarch64 \
    -M virt \
    -cpu $CORTEX \
    -smp 4 \
    -m 8G \
    -kernel "${KERNEL_IMAGE_PATH}" \
    -append "root=/dev/vda${PART_NUMBER} console=ttyAMA0" \
    -drive ${DRIVE_PARAMS} \
    -device virtio-blk-device,drive=usbdisk \
    -netdev user,id=net0,hostfwd=tcp::2222-:22 \
    -device virtio-net-device,netdev=net0 \
    -serial mon:stdio \
    -nographic