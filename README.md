
# Fleet OS QEMU Virtual

Run Fleet OS images by a QEMU appliance.

The system consumes Fleet OS Bootloader and RootFS filesystem, produces bootable image and run the image inside a QEMU VM. 

## Requirements

- Install standard developement tools: gcc, make, ninja, cmake.
  compile_qemu shall print an error when some of the needed dependencies are missing.
- KVM appliance enabled.
- Fleet OS Images (note the processor architecture to be used).
- Copy `target_disk_setup` tool to `tools/` in the git root.

## Build

- QEMU build is needed. Do not use system hosted QEMU!
  Run `./compile_qemu.sh` from the git root
- Copy `target_disk_setup` tool to `tools/` in the git root.

## Run

All commands are run from a bash compatible terminal with
working directory set to the git root.

- Prepare Flees OS images. Absolute paths.
  - `export FOROOTFS=<absolute_path_to_rootfs_img>`
  - `export FOBOOTLOADER=<absolute_path_to_bootloader_img>`
- Run `./fleetos_prepare.sh "${FOROOTFS}" "${FOBOOTLODER}"`
- Check the `fleetos_run.sh` has a correct machine, architecture and partition number set.
- Run `./fleetos_run.sh` - it starts QEMU, opens terminal and forwards SSH to localhost port 2222

The system creates a bootable image as `qemu_images/bootable_image.img` which is then run by QEMU.

## Tests

There are no tests