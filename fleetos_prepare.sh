#!/bin/bash
#
# It mounts disks and prepares the environment for QEMU.
# - copy Kernal image
# - update fstab
#

set -e

SCRIPT_DIR="$(dirname "$(realpath "$0")")"
QEMU_INSTALL_DIR="${SCRIPT_DIR}/qemu_install"
IMAGE_DIR="${SCRIPT_DIR}/images"
QEMU_IMAGE_DIR="${SCRIPT_DIR}/qemu_images"
MOUNT_PATH="${SCRIPT_DIR}/mnt"

KERNEL_IMG_PATH="$QEMU_IMAGE_DIR/kernel.img"
BOOTABLE_IMG_PATH="$QEMU_IMAGE_DIR/bootable_image.img"

ROOTFS_IMG_PATH="$1"
BOOTLOADER_PATH="$2"


#
# Check if the rootfs has a correct format by comparing file extension...
#
check_rootfs_by_extension() {
    local rootfs_path="$1"
    local extension="${rootfs_path##*.}"
    if ! [ "$extension" == "ext4" ]; then
        echo "Unsupported rootfs image format: $extension"
        exit 1
    fi
}

#
# All checks for global vars shall be part of this function!
#
check_vars() {
    if [ -z "$ROOTFS_IMG_PATH" ] || [ -z "$BOOTLOADER_PATH" ]; then
        echo "Usage: $0 <rootfs_image_path> <bootloader_path>"
        exit 1
    fi
    if [ ! -f "$ROOTFS_IMG_PATH" ]; then
        echo "Rootfs image not found at $ROOTFS_IMG_PATH"
        exit 1
    fi
    if [ ! -f "$BOOTLOADER_PATH" ]; then
        echo "Bootloader image not found at $BOOTLOADER_PATH"
        exit 1
    fi
    if [ check_rootfs_by_extension "$ROOTFS_IMG_PATH" ]; then
        echo "Invalid rootfs image format. Only ext4 is supported."
        exit 1
    fi
}

check_vars



echo ""
echo "Preparing QEMU environment..."
echo "QEMU_INSTALL_DIR:  $QEMU_INSTALL_DIR"
echo "IMAGE_DIR:         $IMAGE_DIR"
echo "QEMU_IMAGE_DIR:    $QEMU_IMAGE_DIR"
echo "MOUNT_PATH:        $MOUNT_PATH"
echo "ROOTFS_IMG_PATH:   $ROOTFS_IMG_PATH"
echo "BOOTLOADER_PATH:   $BOOTLOADER_PATH"
echo "KERNEL_IMG_PATH:   $KERNEL_IMG_PATH"
echo "BOOTABLE_IMG_PATH: $BOOTABLE_IMG_PATH"
echo "MOUNT_PATH:        $MOUNT_PATH"
echo "----------------------------------------"
echo ""

cleanup() {
    rm -rf "$MOUNT_PATH"
}

mount_rootfs() {
    mkdir -p "$MOUNT_PATH"
    sudo mount -o loop "$ROOTFS_IMG_PATH" "$MOUNT_PATH"
}

umount_rootfs() {
    sudo umount "$MOUNT_PATH"
}

get_kernel_image() {
    pushd "$MOUNT_PATH"
        cp boot/Image-* "$KERNEL_IMG_PATH"
    popd
}

update_fstab() {
    local fstab_path="$MOUNT_PATH/etc/fstab"
    if [ -f "$fstab_path" ]; then
        # The virtblk device is used --> it is named as vda*
        sudo sed -i "s|/dev/mmcblk0p1|/dev/vda1|g" "$fstab_path"
        sudo sed -i "s|/dev/mmcblk0p2|/dev/vda2|g" "$fstab_path"
        sudo sed -i "s|/dev/mmcblk0p3|/dev/vda3|g" "$fstab_path"
    else
        echo "fstab file not found at $fstab_path"
    fi
}

#
# It creates Bootable image on target machine.
# It is the simplies way how to use ext4 footfs image.
# The image is created by a target_disk_setup for simplicity...
#
create_bootable_image() {
    local target_disk_setup_path="./tools/target_disk_setup"
    if [ ! -f "$target_disk_setup_path" ]; then
        echo "target_disk_setup tool not found at '$target_disk_setup_path'"
        exit 1
    fi
    sudo ./tools/target_disk_setup \
       --bootloader    "$BOOTLOADER_PATH" \
       --rootfs        "$ROOTFS_IMG_PATH" \
       --target-image  "$BOOTABLE_IMG_PATH" \
       --image-size-gb 32
    sudo chown -R $USER:$USER "$QEMU_IMAGE_DIR/"
}

mount_rootfs
get_kernel_image
update_fstab
umount_rootfs

create_bootable_image

cleanup