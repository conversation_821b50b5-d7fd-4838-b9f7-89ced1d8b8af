#!/bin/bash
#
# It mounts disks and prepares the environment for QEMU.
# - copy Kernel image
# - update fstab
#
# Supports both single partition images (.ext4) and whole disk images (.img)
# For .img files, extracts the specified partition (default: 2nd partition)
#
# Usage: $0 <rootfs_image_path> <bootloader_path> [partition_number]
#

set -e

SCRIPT_DIR="$(dirname "$(realpath "$0")")"
QEMU_INSTALL_DIR="${SCRIPT_DIR}/qemu_install"
IMAGE_DIR="${SCRIPT_DIR}/images"
QEMU_IMAGE_DIR="${SCRIPT_DIR}/qemu_images"
MOUNT_PATH="${SCRIPT_DIR}/mnt"

KERNEL_IMG_PATH="$QEMU_IMAGE_DIR/kernel.img"
BOOTABLE_IMG_PATH="$QEMU_IMAGE_DIR/bootable_image.img"

ROOTFS_IMG_PATH="$1"
BOOTLOADER_PATH="$2"
PARTITION_NUMBER="$3"

# Variables for handling .img files
EXTRACTED_ROOTFS_PATH=""
IS_WHOLE_DISK_IMAGE=false


#
# Check if the rootfs has a correct format by comparing file extension...
#
check_rootfs_by_extension() {
    local rootfs_path="$1"
    local extension="${rootfs_path##*.}"
    if ! [ "$extension" == "ext4" ] && ! [ "$extension" == "img" ]; then
        echo "Unsupported rootfs image format: $extension. Supported formats: ext4, img"
        exit 1
    fi
}

#
# All checks for global vars shall be part of this function!
#
check_vars() {
    if [ -z "$ROOTFS_IMG_PATH" ] || [ -z "$BOOTLOADER_PATH" ]; then
        echo "Usage: $0 <rootfs_image_path> <bootloader_path> [partition_number]"
        echo "  partition_number: Which partition to extract from .img files (default: 2)"
        exit 1
    fi
    if [ ! -f "$ROOTFS_IMG_PATH" ]; then
        echo "Rootfs image not found at $ROOTFS_IMG_PATH"
        exit 1
    fi
    if [ ! -f "$BOOTLOADER_PATH" ]; then
        echo "Bootloader image not found at $BOOTLOADER_PATH"
        exit 1
    fi

    # Set default partition number if not provided
    if [ -z "$PARTITION_NUMBER" ]; then
        PARTITION_NUMBER=2
        echo "Using default partition number: $PARTITION_NUMBER"
    else
        # Validate partition number is a positive integer
        if ! [[ "$PARTITION_NUMBER" =~ ^[1-9][0-9]*$ ]]; then
            echo "Invalid partition number: $PARTITION_NUMBER. Must be a positive integer."
            exit 1
        fi
        echo "Using partition number: $PARTITION_NUMBER"
    fi

    if [ check_rootfs_by_extension "$ROOTFS_IMG_PATH" ]; then
        echo "Invalid rootfs image format. Only ext4 and img are supported."
        exit 1
    fi
}

#
# Detect if the image is a whole disk image (.img) or single partition (.ext4)
#
detect_image_type() {
    local rootfs_path="$1"
    local extension="${rootfs_path##*.}"

    if [ "$extension" == "img" ]; then
        # Check if it's a whole disk image by looking for partition table
        if sudo fdisk -l "$rootfs_path" 2>/dev/null | grep -q "^$rootfs_path"; then
            IS_WHOLE_DISK_IMAGE=true
            echo "Detected whole disk image (.img)"
        else
            IS_WHOLE_DISK_IMAGE=false
            echo "Detected single partition image (.img)"
        fi
    else
        IS_WHOLE_DISK_IMAGE=false
        echo "Detected single partition image (.ext4)"
    fi
}

#
# Extract rootfs partition from whole disk image using fdisk and dd
# Uses the configurable PARTITION_NUMBER variable
#
extract_rootfs_partition() {
    local img_path="$1"

    echo "Extracting partition $PARTITION_NUMBER (rootfs) from whole disk image..."

    # Get partition information using fdisk
    local partition_info=$(fdisk -l "$img_path" 2>/dev/null | grep "^${img_path}${PARTITION_NUMBER} ")

    if [ -z "$partition_info" ]; then
        echo "Could not find partition $PARTITION_NUMBER in $img_path"
        echo "Available partitions:"
        fdisk -l "$img_path" 2>/dev/null | grep "^${img_path}" || echo "No partitions found"
        exit 1
    fi

    # Extract start sector and size from fdisk output
    # fdisk output format: device boot start end sectors size id type
    local start_sector=$(echo "$partition_info" | awk '{print $2}')
    local end_sector=$(echo "$partition_info" | awk '{print $3}')
    local sectors=$((end_sector - start_sector + 1))

    echo "Found partition $PARTITION_NUMBER: start=$start_sector, sectors=$sectors"

    # Create extracted rootfs image file
    EXTRACTED_ROOTFS_PATH="${QEMU_IMAGE_DIR}/extracted_rootfs.ext4"
    mkdir -p "$QEMU_IMAGE_DIR"

    echo "Extracting partition to $EXTRACTED_ROOTFS_PATH..."
    # Use dd to extract the partition (512 bytes per sector is standard)
    dd if="$img_path" of="$EXTRACTED_ROOTFS_PATH" bs=512 skip="$start_sector" count="$sectors" status=progress

    echo "Rootfs partition extracted successfully"
}



check_vars

cleanup() {
    rm -rf "$MOUNT_PATH"

    # Clean up extracted rootfs file if it was created
    if [ -n "$EXTRACTED_ROOTFS_PATH" ] && [ -f "$EXTRACTED_ROOTFS_PATH" ]; then
        echo "Cleaning up extracted rootfs: $EXTRACTED_ROOTFS_PATH"
        rm -f "$EXTRACTED_ROOTFS_PATH"
    fi
}

# Set up trap to ensure cleanup on exit
trap cleanup EXIT INT TERM

echo ""
echo "Preparing QEMU environment..."
echo "QEMU_INSTALL_DIR:  $QEMU_INSTALL_DIR"
echo "IMAGE_DIR:         $IMAGE_DIR"
echo "QEMU_IMAGE_DIR:    $QEMU_IMAGE_DIR"
echo "MOUNT_PATH:        $MOUNT_PATH"
echo "ROOTFS_IMG_PATH:   $ROOTFS_IMG_PATH"
echo "BOOTLOADER_PATH:   $BOOTLOADER_PATH"
echo "PARTITION_NUMBER:  $PARTITION_NUMBER"
echo "KERNEL_IMG_PATH:   $KERNEL_IMG_PATH"
echo "BOOTABLE_IMG_PATH: $BOOTABLE_IMG_PATH"
echo "----------------------------------------"
echo ""



mount_rootfs() {
    mkdir -p "$MOUNT_PATH"

    # Use extracted rootfs if we're dealing with a whole disk image
    local image_to_mount="$ROOTFS_IMG_PATH"
    if [ "$IS_WHOLE_DISK_IMAGE" = true ] && [ -n "$EXTRACTED_ROOTFS_PATH" ]; then
        image_to_mount="$EXTRACTED_ROOTFS_PATH"
    fi

    echo "Mounting $image_to_mount at $MOUNT_PATH"
    sudo mount -o loop "$image_to_mount" "$MOUNT_PATH"
}

umount_rootfs() {
    sudo umount "$MOUNT_PATH"
}

get_kernel_image() {
    pushd "$MOUNT_PATH"
        cp boot/Image-* "$KERNEL_IMG_PATH"
    popd
}

update_fstab() {
    local fstab_path="$MOUNT_PATH/etc/fstab"
    if [ -f "$fstab_path" ]; then
        # The virtblk device is used --> it is named as vda*
        sudo sed -i "s|/dev/mmcblk0p1|/dev/vda1|g" "$fstab_path"
        sudo sed -i "s|/dev/mmcblk0p2|/dev/vda2|g" "$fstab_path"
        sudo sed -i "s|/dev/mmcblk0p3|/dev/vda3|g" "$fstab_path"
    else
        echo "fstab file not found at $fstab_path"
    fi
}

#
# It creates Bootable image on target machine.
# It is the simplies way how to use ext4 footfs image.
# The image is created by a target_disk_setup for simplicity...
#
create_bootable_image() {
    local target_disk_setup_path="./tools/target_disk_setup"
    if [ ! -f "$target_disk_setup_path" ]; then
        echo "target_disk_setup tool not found at '$target_disk_setup_path'"
        exit 1
    fi

    # Use extracted rootfs if we're dealing with a whole disk image
    local rootfs_to_use="$ROOTFS_IMG_PATH"
    if [ "$IS_WHOLE_DISK_IMAGE" = true ] && [ -n "$EXTRACTED_ROOTFS_PATH" ]; then
        rootfs_to_use="$EXTRACTED_ROOTFS_PATH"
    fi

    echo "Creating bootable image using rootfs: $rootfs_to_use"
    sudo ./tools/target_disk_setup \
       --bootloader    "$BOOTLOADER_PATH" \
       --rootfs        "$rootfs_to_use" \
       --target-image  "$BOOTABLE_IMG_PATH" \
       --image-size-gb 32
    sudo chown -R $USER:$USER "$QEMU_IMAGE_DIR/"
}

# Detect image type and extract rootfs if needed
detect_image_type "$ROOTFS_IMG_PATH"

if [ "$IS_WHOLE_DISK_IMAGE" = true ]; then
    extract_rootfs_partition "$ROOTFS_IMG_PATH"
fi

mount_rootfs
get_kernel_image
update_fstab
umount_rootfs

create_bootable_image