#!/bin/bash

set -e

QEMU_BRANCH="stable-9.2"
QEMU_GIT_URI="https://github.com/qemu/qemu.git"
SCRIPT_DIR="$(dirname $(realpath $0))"
QEMU_INSTALL_DIR="$SCRIPT_DIR/qemu/qemu_install"

mkdir -p qemu
pushd qemu
    if [ -d "qemu_git" ]; then
        echo "QEMU source directory already exists. Skipping clone."
        echo "If you want to re-clone, please remove the directory: qemu/qemu_git"
    else
        git clone --recursive --branch "$QEMU_BRANCH" --single-branch --depth 1 "$QEMU_GIT_URI" qemu_git
    fi
    mkdir -p $QEMU_INSTALL_DIR
    pushd qemu_git
        mkdir -p build
        pushd build
            ../configure \
                --target-list='aarch64-softmmu,aarch64-linux-user' \
                --enable-slirp \
                --prefix="$QEMU_INSTALL_DIR"
            ninja -j$(nproc)
            ninja install
        popd
    popd
popd